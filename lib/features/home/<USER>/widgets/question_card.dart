import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
// If 'theme_extensions.dart' defines montserratTitleExtraSmall or other specific text styles,
// ensure they are accessible or define similar styles directly.
// import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart';

class QuestionCard extends StatelessWidget {
  final entities.Question question;
  final double progress;
  final String progressText;
  final bool isMandatory;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final bool hasPhotoUrl;
  final num? taskId;
  final num? formId;

  const QuestionCard({
    super.key,
    required this.question,
    required this.progress,
    required this.progressText,
    required this.isMandatory,
    required this.showCameraIcon,
    required this.isCameraMandatory,
    required this.hasPhotoUrl,
    this.taskId,
    this.formId,
  });

  // Helper to get subtitle for the "Add photos" section, similar to original _getSubtitle
  String? _getPhotoSectionSubtitle() {
    if (showCameraIcon || hasPhotoUrl) {
      return 'Full display';
    }
    return null;
  }

  // Helper to get tag for the "Add photos" section, similar to original _getTag
  String? _getPhotoSectionTag() {
    if ((showCameraIcon || hasPhotoUrl) && isCameraMandatory) {
      return 'HI-RES';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final photoSectionSubtitle = _getPhotoSectionSubtitle();
    final photoSectionTag = _getPhotoSectionTag();

    // Define text styles based on the image and typical app theming
    // Main Title Style (e.g., "Multipack Stickering Task")
    final titleTextStyle =
        Theme.of(context).textTheme.montserratTitleExtraSmall;

    // Progress Text Style (e.g., "0 of 5")
    final progressTextStyle = Theme.of(context).textTheme.montserratTableSmall;
    //  Theme.of(context).textTheme.bodyLarge?.copyWith(
    //   ---
    //           // bodyLarge is typically 16sp
    //           color: AppColors.blackTint1, // Use a grey color from AppColors
    //           fontWeight: FontWeight.w500,
    //           fontSize: 16, // As per image
    //         ) ??
    // TextStyle(
    //   // Fallback
    //   color: Colors.grey.shade600,
    //   fontWeight: FontWeight.w500,
    //   fontSize: 16,
    // );

    // "Add photos" Title Style
    final addPhotosTitleStyle =
        Theme.of(context).textTheme.montserratTitleExtraSmall;

    // "Full display" Subtitle Style
    final addPhotosSubtitleStyle =
        Theme.of(context).textTheme.montserratTableSmall; // Fallback

    // "HI-RES" Tag Text Style
    // Using a darker grey or black for better contrast on lightGrey2 background
    final hiResTagTextStyle = Theme.of(context).textTheme.labelMedium?.copyWith(
              // labelMedium is typically 12sp
              fontWeight: FontWeight.bold,
              color:
                  AppColors.black.withOpacity(0.75), // Darker grey for contrast
              fontSize: 12,
            ) ??
        TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.black.withOpacity(0.75));

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(
                10.0), // Slightly more rounded corners for the main card
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(
                    0.05), // Assuming this is a light shadow color like Colors.black.withOpacity(0.1)
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            borderRadius:
                BorderRadius.circular(10.0), // Match container's border radius
            onTap: () => _handleQuestionTap(context),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 1. Title (e.g., "Multipack Stickering Task")
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        question.questionDescription ?? 'Unnamed Question',
                        style: titleTextStyle,
                      ),
                    ),
                    if (isMandatory) ...[
                      const Gap(4),
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.priority_high,
                          color: AppColors.loginRed,
                          size: 16,
                        ),
                      ),
                    ],
                    if (hasPhotoUrl) ...[
                      const Gap(4),
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.blue,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.image,
                          color: Colors.white,
                          size: 10,
                        ),
                      ),
                    ],
                  ],
                ),
                const Gap(12), // Spacing after title

                // 2. Progress bar and text row (e.g., "0 of 5")
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 8, // Thicker progress bar
                        clipBehavior: Clip
                            .antiAlias, // Ensures rounded corners are respected by child
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey2, // Background of the bar
                          borderRadius: BorderRadius.circular(
                              4), // Rounded ends for the bar
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: progress.clamp(0.0, 1.0),
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.primaryBlue, // Progress color
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const Gap(16), // Space between bar and text
                    Text(
                      progressText,
                      style: progressTextStyle,
                    ),
                  ],
                ),

                // Conditionally add space only if the "Add photos" section will be shown
                if (showCameraIcon) const Gap(20),

                // 3. "Add photos" section (conditional based on showCameraIcon)
                // "Add photos" section
                if (showCameraIcon) ...[
                  PhotoUploadWidget(
                    onCameraPressed: () => _handleAddPhotosTap(context),
                    onImagesTap: () => _handleAddPhotosTap(context),
                    selectedImages: const [],
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  // _handleQuestionTap - restored to original logic without MPTPage navigation
  void _handleQuestionTap(BuildContext context) {
    final questionParts = question.questionParts ?? [];
    final hasSignature = question.hasSignature ?? false;
    final isMulti = question.isMulti ?? false;

    if (questionParts.length != 1 || hasSignature) {
      if (isMulti) {
        // Use MaterialPageRoute to pass taskId and formId
        context.pushRoute(FQPDRoute(
          question: question,
          taskId: taskId,
          formId: formId,
        ));
      } else {
        context.pushRoute(SubHeaderRoute(
          title: question.questionDescription ?? 'Details',
          questionParts: questionParts,
          question: question,
          taskId: taskId,
          formId: formId,
        ));
      }
    } else {
      // For now, we'll pass the taskId and formId through a workaround
      // since the route generation needs to be updated
      context.pushRoute(QPMDRoute(
        question: question,
        questionPart: questionParts.first,
        taskId: taskId,
        formId: formId,
      ));
    }
  }

  // Handle navigation to MPTPage when "Add photos" section is tapped
  void _handleAddPhotosTap(BuildContext context) {
    if (showCameraIcon &&
        (question.photoTagsTwo?.isNotEmpty == true ||
            question.photoTagsThree?.isNotEmpty == true)) {
      // Navigate to MPTPage with question and level
      final level = question.photoTagsTwo?.isNotEmpty == true ? 2 : 3;
      context.pushRoute(MPTRoute(
        taskId: taskId?.toString(),
        formId: formId?.toString(),
        questionId: question.questionId?.toString(),
        images: const [],
        question: question,
        level: level,
        questionPartId: null,
      ));
    }
  }
}
